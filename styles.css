/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Enhanced Color Palette */
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Refined Text Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;

    /* Background Colors */
    --background-white: #ffffff;
    --background-light: #f8fafc;
    --background-gray: #f3f4f6;

    /* Border and Divider Colors */
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;

    /* Typography System */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Enhanced Font Size Scale */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-2xl: 1.5rem;      /* 24px */
    --font-size-3xl: 1.875rem;    /* 30px */
    --font-size-4xl: 2.25rem;     /* 36px */
    --font-size-5xl: 3rem;        /* 48px */

    /* Consistent Spacing Scale */
    --spacing-0: 0;
    --spacing-1: 0.25rem;         /* 4px */
    --spacing-2: 0.5rem;          /* 8px */
    --spacing-3: 0.75rem;         /* 12px */
    --spacing-4: 1rem;            /* 16px */
    --spacing-5: 1.25rem;         /* 20px */
    --spacing-6: 1.5rem;          /* 24px */
    --spacing-8: 2rem;            /* 32px */
    --spacing-10: 2.5rem;         /* 40px */
    --spacing-12: 3rem;           /* 48px */
    --spacing-16: 4rem;           /* 64px */
    --spacing-20: 5rem;           /* 80px */
    --spacing-24: 6rem;           /* 96px */

    /* Legacy spacing for backward compatibility */
    --spacing-xs: var(--spacing-1);
    --spacing-sm: var(--spacing-2);
    --spacing-md: var(--spacing-4);
    --spacing-lg: var(--spacing-6);
    --spacing-xl: var(--spacing-8);
    --spacing-2xl: var(--spacing-12);
    --spacing-3xl: var(--spacing-16);

    /* Enhanced Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;

    /* Professional Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transition System */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    --line-height-loose: 2;

    /* Container Widths */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* Enhanced Base Styles */
body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    background-color: var(--background-white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Container System */
.container {
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    width: 100%;
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--spacing-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-8);
    }
}

/* Enhanced Typography System */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: var(--spacing-6);
    letter-spacing: -0.025em;
}

h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-8);
}

h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-6);
}

h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-4);
}

h4 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

h5 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-3);
}

h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-3);
}

/* Enhanced Paragraph and Text Styles */
p {
    margin-bottom: var(--spacing-4);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

.text-large {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
}

.text-small {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.text-muted {
    color: var(--text-muted);
}

.text-center {
    text-align: center;
}

/* Enhanced List Styles */
ul, ol {
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-6);
}

li {
    margin-bottom: var(--spacing-2);
    line-height: var(--line-height-relaxed);
}

/* Enhanced Link Styles */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-normal);
    font-weight: var(--font-weight-medium);
}

a:hover {
    color: var(--secondary-color);
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Enhanced Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    cursor: pointer;
    transition: var(--transition-normal);
    min-height: 44px; /* Improved touch target */
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Primary Button */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:active {
    transform: translateY(0);
}

/* Outline Button */
.btn-outline {
    background-color: transparent;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-outline:hover {
    background-color: var(--background-light);
    color: var(--text-primary);
    border-color: var(--text-secondary);
    transform: translateY(-1px);
}

.btn-outline:active {
    transform: translateY(0);
}

/* Button Sizes */
.btn-small {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

.btn-xl {
    padding: var(--spacing-5) var(--spacing-10);
    font-size: var(--font-size-xl);
    min-height: 60px;
}

/* Enhanced Header and Navigation */
.header {
    background-color: var(--background-white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--border-light);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) 0;
    min-height: 80px;
}

.logo {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.logo h1 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin: 0;
    letter-spacing: -0.025em;
}

.tagline {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-2);
    margin: 0;
    align-items: center;
}

.nav-link {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
    color: var(--text-secondary);
    position: relative;
    min-height: 44px;
    display: flex;
    align-items: center;
}

.nav-link:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
    min-height: 44px;
    min-width: 44px;
    align-items: center;
    justify-content: center;
}

.nav-toggle:hover {
    background-color: var(--background-light);
}

.nav-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    transition: var(--transition-normal);
    border-radius: 1px;
}

/* Enhanced Hero Section */
.hero {
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
    padding: var(--spacing-20) 0 var(--spacing-24);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 600px;
}

.hero-content h2 {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-6);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
}

.hero-content p {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-10);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
    align-items: center;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-placeholder {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    text-align: center;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
}

.hero-placeholder:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.hero-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* Enhanced Hero Image with Stock Photo */
.hero-image-container {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
}

.hero-image-container:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.hero-stock-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: var(--transition-slow);
}

.hero-image-container:hover .hero-stock-image {
    transform: scale(1.05);
}

.hero-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(37, 99, 235, 0.9), transparent);
    color: white;
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-overlay span {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    text-align: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Enhanced Services Section */
.services-preview {
    padding: var(--spacing-20) 0;
    background-color: var(--background-white);
    position: relative;
}

.services-preview h2 {
    text-align: center;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.services-preview > .container > p {
    text-align: center;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-16);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-8);
}

.service-card {
    background-color: var(--background-white);
    padding: var(--spacing-10);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-icon {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-6);
    display: block;
    line-height: 1;
}

.service-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
}

.service-card p {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: 0;
}

/* Enhanced Newsletter Section */
.newsletter-signup {
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background-gray) 100%);
    padding: var(--spacing-20) 0;
    position: relative;
}

.newsletter-signup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 80%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.newsletter-content {
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 1;
}

.newsletter-content h2 {
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
}

.newsletter-content > p {
    margin-bottom: var(--spacing-12);
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

.newsletter-form {
    background-color: var(--background-white);
    padding: var(--spacing-10);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
}

.form-group {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    align-items: stretch;
}

.form-group input[type="email"] {
    flex: 1;
    padding: var(--spacing-4) var(--spacing-5);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    transition: var(--transition-normal);
    min-height: 52px;
}

.form-group input[type="email"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input[type="email"]::placeholder {
    color: var(--text-muted);
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
    justify-content: center;
}

.form-checkbox input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
}

.form-checkbox label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.privacy-note {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.privacy-note a {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

/* Enhanced About Preview Section */
.about-preview {
    padding: var(--spacing-20) 0;
    background-color: var(--background-white);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.about-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-6);
}

.about-text p {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-6);
}

.about-text p:last-of-type {
    margin-bottom: var(--spacing-10);
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.stat {
    text-align: center;
    padding: var(--spacing-8);
    background: linear-gradient(135deg, var(--background-light), var(--background-white));
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-light);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.stat:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.stat-number {
    display: block;
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-2);
    line-height: var(--line-height-tight);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, var(--text-primary) 0%, #111827 100%);
    color: white;
    padding: var(--spacing-20) 0 var(--spacing-8);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-12);
}

.footer-section h3,
.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-6);
    font-weight: var(--font-weight-semibold);
}

.footer-section h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-4);
}

.footer-section p {
    color: #d1d5db;
    margin-bottom: var(--spacing-3);
    line-height: var(--line-height-relaxed);
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: var(--spacing-3);
}

.footer-section a {
    color: #d1d5db;
    transition: var(--transition-normal);
    font-weight: var(--font-weight-medium);
}

.footer-section a:hover {
    color: white;
    transform: translateX(2px);
}

.social-links {
    display: flex;
    gap: var(--spacing-4);
    margin-top: var(--spacing-6);
}

.social-links a {
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-8);
    text-align: center;
    color: #9ca3af;
    font-size: var(--font-size-sm);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .hero .container {
        gap: var(--spacing-12);
    }

    .hero-content h2 {
        font-size: var(--font-size-4xl);
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-6);
    }
}

@media (max-width: 768px) {
    /* Enhanced Mobile Navigation */
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--background-white);
        flex-direction: column;
        padding: var(--spacing-8);
        box-shadow: var(--shadow-xl);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
        border-top: 1px solid var(--border-light);
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-menu li {
        margin-bottom: var(--spacing-2);
    }

    .nav-link {
        padding: var(--spacing-4);
        font-size: var(--font-size-base);
        border-radius: var(--border-radius);
        width: 100%;
        text-align: center;
    }

    .nav-toggle {
        display: flex;
    }

    /* Mobile Hero Section */
    .hero {
        padding: var(--spacing-16) 0 var(--spacing-20);
    }

    .hero .container {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
        text-align: center;
    }

    .hero-content h2 {
        font-size: var(--font-size-4xl);
    }

    .hero-content p {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-4);
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .hero-image-container {
        height: 300px;
    }

    /* Mobile About Section */
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
        text-align: center;
    }

    .about-stats {
        flex-direction: row;
        justify-content: space-around;
        flex-wrap: wrap;
        gap: var(--spacing-4);
    }

    .stat {
        flex: 1;
        min-width: 120px;
    }

    /* Mobile Forms */
    .form-group {
        flex-direction: column;
        gap: var(--spacing-4);
    }

    .form-group .btn {
        width: 100%;
    }

    /* Mobile Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    /* Mobile Spacing Adjustments */
    .services-preview,
    .newsletter-signup,
    .about-preview {
        padding: var(--spacing-16) 0;
    }
}

@media (max-width: 480px) {
    /* Small Mobile Adjustments */
    .container {
        padding: 0 var(--spacing-4);
    }

    .hero {
        padding: var(--spacing-12) 0 var(--spacing-16);
    }

    .hero-content h2 {
        font-size: var(--font-size-3xl);
        line-height: var(--line-height-tight);
    }

    .hero-content p {
        font-size: var(--font-size-base);
    }

    .hero-image-container {
        height: 250px;
    }

    .about-stats {
        flex-direction: column;
        gap: var(--spacing-4);
    }

    .stat {
        padding: var(--spacing-6);
    }

    .stat-number {
        font-size: var(--font-size-3xl);
    }

    .services-preview,
    .newsletter-signup,
    .about-preview {
        padding: var(--spacing-12) 0;
    }

    .newsletter-form {
        padding: var(--spacing-6);
    }

    .service-card {
        padding: var(--spacing-6);
    }

    .service-icon {
        font-size: var(--font-size-4xl);
    }

    /* Ensure minimum font size for readability */
    body {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    input, textarea, select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Page-specific styles */
.page-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: var(--spacing-3xl) 0;
    text-align: center;
}

.page-hero h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
}

.company-info {
    padding: var(--spacing-3xl) 0;
}

.info-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-3xl);
}

.info-content h2 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.info-content h3 {
    color: var(--primary-color);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
}

.company-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.detail-card {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--primary-color);
}

.detail-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    margin-top: 0;
}

.detail-item {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
}

.detail-item strong {
    color: var(--text-primary);
    display: inline-block;
    min-width: 120px;
}

.locations {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.section-subtitle {
    text-align: center;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
}

.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.location-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.location-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.location-details p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.map-placeholder {
    height: 150px;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-top: var(--spacing-md);
}

/* Enhanced Location Images */
.location-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center;
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
}

.location-map {
    position: relative;
    margin-top: var(--spacing-6);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.location-map:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.location-map:hover .location-image {
    transform: scale(1.05);
}

.location-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(37, 99, 235, 0.9), transparent);
    color: white;
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.location-overlay span {
    font-weight: var(--font-weight-semibold);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.values {
    padding: var(--spacing-3xl) 0;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.value-card {
    background-color: var(--background-white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.value-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
}

.value-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.team-section {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.expertise-areas {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.expertise-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.expertise-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.expertise-card ul {
    list-style: none;
    margin-top: var(--spacing-md);
}

.expertise-card li {
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.expertise-card li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.cta-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: var(--spacing-md);
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn-primary {
    background-color: white;
    color: var(--primary-color);
    border-color: white;
}

.cta-buttons .btn-primary:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.cta-buttons .btn-secondary {
    background-color: transparent;
    color: white;
    border-color: white;
}

.cta-buttons .btn-secondary:hover {
    background-color: white;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }

    .locations-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .expertise-areas {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero {
        padding: var(--spacing-xl) 0;
    }

    .hero-content h2 {
        font-size: var(--font-size-3xl);
    }

    .about-stats {
        flex-direction: column;
    }

    .page-hero h1 {
        font-size: var(--font-size-3xl);
    }

    .values-grid {
        grid-template-columns: 1fr;
    }
}

/* Services Page Styles */
.services-overview {
    padding: var(--spacing-3xl) 0;
}

.services-intro {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.services-intro h2 {
    margin-bottom: var(--spacing-lg);
}

.services-detailed {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3xl);
}

.service-detail {
    background-color: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.service-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.service-header .service-icon {
    font-size: var(--font-size-4xl);
}

.service-header h3 {
    color: white;
    margin: 0;
    font-size: var(--font-size-2xl);
}

.service-content {
    padding: var(--spacing-xl);
}

.service-content h4 {
    color: var(--primary-color);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.service-features {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.service-features li {
    padding: var(--spacing-sm) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.service-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.service-benefits {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-lg);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.benefit-icon {
    font-size: var(--font-size-lg);
}

.process-section {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.process-section h2 {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.process-hero-image {
    margin: var(--spacing-12) 0;
    text-align: center;
}

.process-image {
    width: 100%;
    max-width: 800px;
    height: 300px;
    object-fit: cover;
    object-position: center;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
}

.process-image:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.process-step {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 auto var(--spacing-lg);
}

.process-step h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.testimonials {
    padding: var(--spacing-3xl) 0;
}

.testimonials h2 {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.testimonial-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
}

.testimonial-content {
    margin-bottom: var(--spacing-lg);
}

.testimonial-content p {
    font-style: italic;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin: 0;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info h4 {
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-base);
}

.author-info span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.testimonial-rating {
    font-size: var(--font-size-lg);
}

@media (max-width: 768px) {
    .services-detailed {
        gap: var(--spacing-xl);
    }

    .service-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-author {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

/* Contact Page Styles */
.contact-section {
    padding: var(--spacing-3xl) 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
}

.contact-info h2 {
    margin-bottom: var(--spacing-lg);
}

.contact-details {
    margin: var(--spacing-2xl) 0;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.contact-icon {
    font-size: var(--font-size-2xl);
    width: 50px;
    height: 50px;
    background-color: var(--background-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-text h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    margin-top: 0;
}

.contact-text p {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.social-contact {
    margin-top: var(--spacing-2xl);
}

.social-contact h3 {
    margin-bottom: var(--spacing-md);
}

.social-contact .social-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--background-light);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.social-icon {
    font-size: var(--font-size-lg);
}

.form-container {
    background-color: var(--background-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.form-container h2 {
    margin-bottom: var(--spacing-md);
}

.form-container > p {
    margin-bottom: var(--spacing-xl);
    color: var(--text-secondary);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-checkboxes {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.checkbox-group label {
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--text-secondary);
}

.checkbox-group label a {
    color: var(--primary-color);
    text-decoration: underline;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.form-note {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    text-align: center;
    margin-top: var(--spacing-md);
    margin-bottom: 0;
}

.form-note a {
    color: var(--primary-color);
    text-decoration: underline;
}

.office-locations {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.office-locations h2 {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.office-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.office-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.office-details p {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.office-details strong {
    color: var(--text-primary);
}

.office-map {
    margin-top: var(--spacing-lg);
}

.office-map .map-placeholder {
    height: 150px;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.office-map .map-placeholder:hover {
    transform: scale(1.02);
}

.office-map .map-placeholder small {
    font-size: var(--font-size-xs);
    opacity: 0.8;
    margin-top: var(--spacing-xs);
}

.faq-section {
    padding: var(--spacing-3xl) 0;
}

.faq-section h2 {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.faq-item {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--primary-color);
}

.faq-item h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.faq-item p {
    margin: 0;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .social-contact .social-links {
        grid-template-columns: 1fr;
    }

    .offices-grid {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .form-container {
        padding: var(--spacing-lg);
    }
}

/* Newsletter Page Styles */
.newsletter-main {
    padding: var(--spacing-3xl) 0;
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.newsletter-info h2 {
    margin-bottom: var(--spacing-lg);
}

.newsletter-benefits {
    margin: var(--spacing-2xl) 0;
}

.newsletter-benefits h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.benefits-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.benefits-list li {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.benefits-list .benefit-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
}

.benefits-list strong {
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.benefits-list p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.newsletter-stats {
    display: flex;
    justify-content: space-around;
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-top: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.newsletter-form-section .form-container {
    position: sticky;
    top: calc(var(--spacing-3xl) + 80px);
}

.form-subtitle {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.checkbox-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.checkbox-item label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
}

.form-consent {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.consent-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.consent-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.consent-item label {
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--text-primary);
}

.privacy-assurance {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-lg);
}

.privacy-assurance h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    margin-top: 0;
}

.privacy-assurance p {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
}

.privacy-list {
    list-style: none;
    margin: var(--spacing-md) 0;
}

.privacy-list li {
    padding: var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    color: var(--success-color);
}

.double-optin-info {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.double-optin-info h2 {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.optin-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin: var(--spacing-2xl) 0;
}

.optin-step {
    background-color: var(--background-white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
}

.step-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
}

.optin-step h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.optin-step p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.record-keeping {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-2xl);
}

.record-keeping h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.record-keeping ul {
    margin: var(--spacing-md) 0;
}

.record-keeping li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.newsletter-samples {
    padding: var(--spacing-3xl) 0;
}

.newsletter-samples h2 {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.samples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.sample-card {
    background-color: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.sample-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-lg);
}

.sample-header h3 {
    color: white;
    margin: 0 0 var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.sample-date {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.sample-content {
    padding: var(--spacing-lg);
}

.sample-content h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    margin-top: 0;
}

.sample-content ul {
    margin: 0;
}

.sample-content li {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.unsubscribe-info {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.unsubscribe-info h2 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.unsubscribe-info > .container > p {
    text-align: center;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2xl);
    color: var(--text-secondary);
}

.unsubscribe-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    background-color: var(--background-white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.feature-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.feature-item h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    margin-top: 0;
}

.feature-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .newsletter-form-section .form-container {
        position: static;
    }

    .newsletter-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }

    .optin-steps {
        grid-template-columns: 1fr;
    }

    .samples-grid {
        grid-template-columns: 1fr;
    }

    .unsubscribe-features {
        grid-template-columns: 1fr;
    }
}

/* Privacy Policy Page Styles */
.last-updated {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-md);
    font-style: italic;
}

.privacy-content {
    padding: var(--spacing-3xl) 0;
}

.privacy-content .container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.privacy-nav {
    position: sticky;
    top: calc(var(--spacing-xl) + 80px);
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.privacy-nav h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    margin-top: 0;
    font-size: var(--font-size-lg);
}

.privacy-nav ul {
    list-style: none;
}

.privacy-nav li {
    margin-bottom: var(--spacing-sm);
}

.privacy-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    display: block;
    transition: all 0.3s ease;
}

.privacy-nav a:hover {
    background-color: var(--primary-color);
    color: white;
}

.privacy-main {
    max-width: none;
}

.privacy-section {
    margin-bottom: var(--spacing-3xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.privacy-section:last-child {
    border-bottom: none;
}

.privacy-section h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-2xl);
}

.privacy-section h3 {
    color: var(--text-primary);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.privacy-section h4 {
    color: var(--text-primary);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.privacy-section ul {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

.privacy-section li {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    line-height: 1.6;
}

.privacy-section strong {
    color: var(--text-primary);
}

.highlight-box {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--primary-color);
    margin: var(--spacing-lg) 0;
}

.highlight-box h4 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: var(--spacing-md);
}

.highlight-box ul {
    margin-bottom: 0;
}

.rights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.right-item {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--accent-color);
}

.right-item h4 {
    color: var(--text-primary);
    margin-top: 0;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.right-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin: var(--spacing-lg) 0;
}

.contact-method {
    background-color: var(--background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
}

.contact-method h4 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: var(--spacing-md);
}

.contact-method p {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.contact-method a {
    color: var(--primary-color);
    text-decoration: underline;
}

@media (max-width: 1024px) {
    .privacy-content .container {
        grid-template-columns: 200px 1fr;
        gap: var(--spacing-xl);
    }

    .privacy-nav {
        padding: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .privacy-content .container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .privacy-nav {
        position: static;
        order: 2;
        margin-top: var(--spacing-xl);
    }

    .privacy-main {
        order: 1;
    }

    .rights-grid {
        grid-template-columns: 1fr;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }
}
